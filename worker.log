2025-05-24 15:18:19,498 - __main__ - ERROR - Error starting worker: <PERSON>() got an unexpected keyword argument 'consumer'
2025-05-24 19:00:23,576 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-24 19:00:23,577 - __main__ - INFO - Starting Huey worker for file processing...
2025-05-24 19:00:23,577 - __main__ - INFO - Queue database: queue_data\huey_queue.db
2025-05-24 19:00:23,577 - __main__ - INFO - Worker is ready to process tasks
2025-05-24 19:00:23,577 - huey.consumer - INFO - Huey consumer started with 1 thread, PID 26628 at 2025-05-24 12:00:23.577556
2025-05-24 19:00:23,577 - huey.consumer - INFO - Scheduler runs every 1 second(s).
2025-05-24 19:00:23,578 - huey.consumer - INFO - Periodic tasks are enabled.
2025-05-24 19:00:23,578 - huey.consumer - INFO - The following commands are available:
+ services.queue_service.process_file_task
2025-05-24 19:01:57,870 - huey.consumer - INFO - Received SIGINT
2025-05-24 19:01:57,870 - huey.consumer - INFO - Shutting down gracefully...
2025-05-24 19:02:00,066 - huey.consumer - INFO - All workers have stopped.
2025-05-24 19:02:00,066 - huey.consumer - INFO - Consumer exiting.
2025-05-24 19:02:39,193 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-24 19:02:39,193 - __main__ - INFO - Starting Huey worker for file processing...
2025-05-24 19:02:39,193 - __main__ - INFO - Queue database: queue_data\huey_queue.db
2025-05-24 19:02:39,193 - __main__ - INFO - Worker is ready to process tasks
2025-05-24 19:02:39,193 - huey.consumer - INFO - Huey consumer started with 1 thread, PID 27092 at 2025-05-24 12:02:39.193377
2025-05-24 19:02:39,193 - huey.consumer - INFO - Scheduler runs every 1 second(s).
2025-05-24 19:02:39,193 - huey.consumer - INFO - Periodic tasks are enabled.
2025-05-24 19:02:39,193 - huey.consumer - INFO - The following commands are available:
+ services.queue_service.process_file_task
2025-05-24 19:02:43,135 - huey.consumer - INFO - Received SIGINT
2025-05-24 19:02:43,135 - huey.consumer - INFO - Shutting down gracefully...
2025-05-24 19:02:43,254 - huey.consumer - INFO - All workers have stopped.
2025-05-24 19:02:43,255 - huey.consumer - INFO - Consumer exiting.
2025-05-24 19:02:45,238 - __main__ - INFO - Worker stopped by user
2025-05-24 19:02:58,858 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-24 19:02:58,858 - __main__ - INFO - Starting Huey worker for file processing...
2025-05-24 19:02:58,858 - __main__ - INFO - Queue database: queue_data\huey_queue.db
2025-05-24 19:02:58,858 - __main__ - INFO - Worker is ready to process tasks
2025-05-24 19:02:58,858 - huey.consumer - INFO - Huey consumer started with 1 thread, PID 10584 at 2025-05-24 12:02:58.858456
2025-05-24 19:02:58,858 - huey.consumer - INFO - Scheduler runs every 1 second(s).
2025-05-24 19:02:58,858 - huey.consumer - INFO - Periodic tasks are enabled.
2025-05-24 19:02:58,858 - huey.consumer - INFO - The following commands are available:
+ services.queue_service.process_file_task
2025-05-24 19:03:56,599 - huey - INFO - Executing services.queue_service.process_file_task: a710639b-ab34-4f57-b06d-1f34b328f128
2025-05-24 19:03:56,602 - services.queue_service - INFO - Starting background processing for file file-s0KpAsyJEIMMwInk
2025-05-24 19:03:57,000 - services.s3_file_service - WARNING - URL not accessible: https://cdn.redai.vn/knowledge_files/4/34af6a9f-47c7-4012-8c78-4099b719689b-Example.pdf, status code: 404
2025-05-24 19:03:57,008 - huey - INFO - services.queue_service.process_file_task: a710639b-ab34-4f57-b06d-1f34b328f128 executed in 0.408s
2025-05-24 19:04:39,675 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-24 19:04:39,675 - __main__ - INFO - Starting Huey worker for file processing...
2025-05-24 19:04:39,675 - __main__ - INFO - Queue database: queue_data\huey_queue.db
2025-05-24 19:04:39,675 - __main__ - INFO - Worker is ready to process tasks
2025-05-24 19:04:39,676 - huey.consumer - INFO - Huey consumer started with 1 thread, PID 5768 at 2025-05-24 12:04:39.676022
2025-05-24 19:04:39,676 - huey.consumer - INFO - Scheduler runs every 1 second(s).
2025-05-24 19:04:39,676 - huey.consumer - INFO - Periodic tasks are enabled.
2025-05-24 19:04:39,676 - huey.consumer - INFO - The following commands are available:
+ services.queue_service.process_file_task
2025-05-24 19:05:18,699 - huey.consumer - INFO - Received SIGINT
2025-05-24 19:05:18,700 - huey.consumer - INFO - Shutting down gracefully...
2025-05-24 19:05:20,980 - huey.consumer - INFO - Received SIGINT
2025-05-24 19:05:20,980 - huey.consumer - INFO - Shutting down gracefully...
2025-05-24 19:05:23,172 - huey.consumer - INFO - All workers have stopped.
2025-05-24 19:05:23,172 - huey.consumer - INFO - Consumer exiting.
2025-05-24 19:05:23,492 - huey.consumer - INFO - All workers have stopped.
2025-05-24 19:05:23,492 - huey.consumer - INFO - Consumer exiting.
2025-05-24 19:06:07,141 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-24 19:06:07,141 - __main__ - INFO - Starting Huey worker for file processing...
2025-05-24 19:06:07,141 - __main__ - INFO - Queue database: queue_data\huey_queue.db
2025-05-24 19:06:07,141 - __main__ - INFO - Worker is ready to process tasks
2025-05-24 19:06:07,141 - huey.consumer - INFO - Huey consumer started with 1 thread, PID 26332 at 2025-05-24 12:06:07.141484
2025-05-24 19:06:07,141 - huey.consumer - INFO - Scheduler runs every 1 second(s).
2025-05-24 19:06:07,141 - huey.consumer - INFO - Periodic tasks are enabled.
2025-05-24 19:06:07,141 - huey.consumer - INFO - The following commands are available:
+ services.queue_service.process_file_task
2025-05-24 19:07:23,629 - huey - INFO - Executing services.queue_service.process_file_task: 221659f5-e34e-42bd-835d-861131c59ecb
2025-05-24 19:07:23,629 - services.queue_service - INFO - Starting background processing for file file-DKHnuf9aXzc4AmGv
2025-05-24 19:07:23,799 - core.s3_client - INFO - Storage key has CDN prefix: https://cdn.redai.vn/1747989585732-5f625715-fc5f-4b5e-bbb2-ae0c7d8665d0.docx
2025-05-24 19:07:23,799 - core.s3_client - INFO - Downloading from CDN URL to temp_file-DKHnuf9aXzc4AmGv_4c78bc3c-b6aa-4b66-bd37-8a3ada17abfa.docx
2025-05-24 19:07:24,500 - core.s3_client - INFO - Successfully downloaded from CDN to temp_file-DKHnuf9aXzc4AmGv_4c78bc3c-b6aa-4b66-bd37-8a3ada17abfa.docx
2025-05-24 19:07:24,500 - services.s3_file_service - INFO - Downloaded file to temporary path: temp-s3\temp_file-DKHnuf9aXzc4AmGv_4c78bc3c-b6aa-4b66-bd37-8a3ada17abfa.docx
2025-05-24 19:07:24,526 - services.markdown_service - INFO - Converting file to Markdown: temp-s3\temp_file-DKHnuf9aXzc4AmGv_4c78bc3c-b6aa-4b66-bd37-8a3ada17abfa.docx (type: .docx)
2025-05-24 19:07:24,621 - services.markdown_service - INFO - Converting Word document to Markdown
2025-05-24 19:07:27,770 - services.markdown_service - INFO - Successfully converted file to Markdown (59436 characters)
2025-05-24 19:07:27,774 - services.chunk_service - INFO - Splitting markdown into chunks using LangChain (size: 2000, overlap: 100)
2025-05-24 19:07:27,776 - services.chunk_service - INFO - MarkdownTextSplitter produced 35 well-sized chunks
2025-05-24 19:07:27,776 - services.document_chunks_service - INFO - Split content into 35 chunks for file file-DKHnuf9aXzc4AmGv
2025-05-24 19:07:27,990 - services.document_chunks_service - INFO - Successfully saved 35 chunks for file file-DKHnuf9aXzc4AmGv
2025-05-24 19:07:27,994 - services.queue_service - ERROR - Error processing file file-DKHnuf9aXzc4AmGv: module 'services.document_chunks_service' has no attribute 'process_embeddings_for_chunks'
2025-05-24 19:07:28,006 - utils.cleanup - INFO - Removed temporary file: temp_file-DKHnuf9aXzc4AmGv_4c78bc3c-b6aa-4b66-bd37-8a3ada17abfa.docx
2025-05-24 19:07:28,007 - huey - INFO - services.queue_service.process_file_task: 221659f5-e34e-42bd-835d-861131c59ecb executed in 4.378s
2025-05-24 19:10:11,053 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-24 19:10:11,054 - __main__ - INFO - Starting Huey worker for file processing...
2025-05-24 19:10:11,054 - __main__ - INFO - Queue database: queue_data\huey_queue.db
2025-05-24 19:10:11,054 - __main__ - INFO - Worker is ready to process tasks
2025-05-24 19:10:11,054 - huey.consumer - INFO - Huey consumer started with 1 thread, PID 7312 at 2025-05-24 12:10:11.054688
2025-05-24 19:10:11,054 - huey.consumer - INFO - Scheduler runs every 1 second(s).
2025-05-24 19:10:11,055 - huey.consumer - INFO - Periodic tasks are enabled.
2025-05-24 19:10:11,055 - huey.consumer - INFO - The following commands are available:
+ services.queue_service.process_file_task
2025-05-24 19:10:25,602 - huey.consumer - INFO - Received SIGINT
2025-05-24 19:10:25,602 - huey.consumer - INFO - Shutting down gracefully...
2025-05-24 19:10:27,006 - huey.consumer - INFO - All workers have stopped.
2025-05-24 19:10:27,007 - huey.consumer - INFO - Consumer exiting.
2025-05-24 19:11:44,660 - huey - INFO - Executing services.queue_service.process_file_task: 97210c43-4f35-435d-9263-b475175e09fd
2025-05-24 19:11:44,675 - services.queue_service - INFO - Starting background processing for file file-275QSXVm1XZ5e1yp
2025-05-24 19:11:44,943 - core.s3_client - INFO - Downloading from R2: dummy.pdf to temp_file-275QSXVm1XZ5e1yp_ea61b569-6a29-4f8e-a9f1-f1ebfd2ff158.pdf
2025-05-24 19:11:46,286 - core.s3_client - ERROR - S3 client error downloading dummy.pdf: An error occurred (404) when calling the HeadObject operation: Not Found
2025-05-24 19:11:46,286 - services.s3_file_service - ERROR - Error downloading file to temp: Failed to download file from storage: An error occurred (404) when calling the HeadObject operation: Not Found
2025-05-24 19:11:46,286 - services.queue_service - ERROR - Error processing file file-275QSXVm1XZ5e1yp: Failed to download file from storage: An error occurred (404) when calling the HeadObject operation: Not Found
2025-05-24 19:11:46,469 - huey - INFO - services.queue_service.process_file_task: 97210c43-4f35-435d-9263-b475175e09fd executed in 1.800s
2025-05-24 19:12:17,164 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-24 19:12:17,165 - __main__ - INFO - Starting Huey worker for file processing...
2025-05-24 19:12:17,165 - __main__ - INFO - Queue database: queue_data\huey_queue.db
2025-05-24 19:12:17,165 - __main__ - INFO - Worker is ready to process tasks
2025-05-24 19:12:17,165 - huey.consumer - INFO - Huey consumer started with 1 thread, PID 23800 at 2025-05-24 12:12:17.165377
2025-05-24 19:12:17,165 - huey.consumer - INFO - Scheduler runs every 1 second(s).
2025-05-24 19:12:17,165 - huey.consumer - INFO - Periodic tasks are enabled.
2025-05-24 19:12:17,165 - huey.consumer - INFO - The following commands are available:
+ services.queue_service.process_file_task
2025-05-24 19:12:26,011 - huey - INFO - Executing services.queue_service.process_file_task: 90985caf-c308-479a-a440-ccb03eca1aa2
2025-05-24 19:12:26,402 - core.s3_client - INFO - Downloading from R2: dummy.pdf to temp_file-ldIgmjKYTWnuq2VY_0645bc26-6407-4a9c-a063-2a47e92f94d7.pdf
2025-05-24 19:12:27,289 - core.s3_client - ERROR - S3 client error downloading dummy.pdf: An error occurred (404) when calling the HeadObject operation: Not Found
2025-05-24 19:12:27,289 - services.s3_file_service - ERROR - Error downloading file to temp: Failed to download file from storage: An error occurred (404) when calling the HeadObject operation: Not Found
2025-05-24 19:12:27,529 - huey - INFO - services.queue_service.process_file_task: 90985caf-c308-479a-a440-ccb03eca1aa2 executed in 1.517s
2025-05-24 19:12:55,148 - huey.consumer - INFO - Received SIGINT
2025-05-24 19:12:55,148 - huey.consumer - INFO - Shutting down gracefully...
2025-05-24 19:12:55,900 - huey.consumer - INFO - All workers have stopped.
2025-05-24 19:12:55,901 - huey.consumer - INFO - Consumer exiting.
2025-05-27 08:57:45,596 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-27 08:57:45,596 - __main__ - INFO - Starting Huey worker for file processing...
2025-05-27 08:57:45,596 - __main__ - INFO - Queue database: queue_data\huey_queue.db
2025-05-27 08:57:45,596 - __main__ - INFO - Worker is ready to process tasks
2025-05-27 08:57:45,596 - huey.consumer - INFO - Huey consumer started with 1 thread, PID 1868 at 2025-05-27 01:57:45.596917
2025-05-27 08:57:45,596 - huey.consumer - INFO - Scheduler runs every 1 second(s).
2025-05-27 08:57:45,597 - huey.consumer - INFO - Periodic tasks are enabled.
2025-05-27 08:57:45,597 - huey.consumer - INFO - The following commands are available:
+ services.queue_service.process_file_task
